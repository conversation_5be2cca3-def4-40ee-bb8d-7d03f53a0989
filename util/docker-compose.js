const { execSync } = require('child_process');
const path = require('path');
const os = require('os');

/**
 * Detecta qual comando do Docker Compose está disponível no sistema
 * Prioriza docker-compose (v1) que é mais estável no Windows
 * @returns {string} O comando do Docker Compose ('docker-compose' ou 'docker compose')
 * @throws {Error} Se nenhum comando estiver disponível
 */
function getDockerComposeCommand() {
    // Primeiro tenta docker-compose (v1) que é mais estável no Windows
    try {
        execSync('docker-compose version', { stdio: 'ignore' });
        return 'docker-compose';
    } catch (error) {
        // Se não encontrar docker-compose, tenta docker compose (v2)
        try {
            execSync('docker compose version', { stdio: 'ignore' });
            return 'docker compose';
        } catch (error) {
            throw new Error('Nenhum dos comandos docker-compose ou docker compose estão disponíveis');
        }
    }
}

/**
 * Normaliza caminhos de arquivos para serem compatíveis entre Windows e Linux
 * @param {string} filePath - Caminho do arquivo
 * @returns {string} Caminho normalizado
 */
function normalizeFilePath(filePath) {
    // Normaliza o caminho e converte barras invertidas para barras normais
    return path.normalize(filePath).replace(/\\/g, '/');
}

/**
 * Executa comando Docker Compose com tratamento de erros específicos para Windows
 * @param {string} command - Comando a ser executado
 * @param {object} options - Opções para execSync
 * @returns {string} Output do comando
 */
function executeDockerComposeCommand(command, options = {}) {
    const isWindows = os.platform() === 'win32';
    const defaultOptions = {
        encoding: 'utf-8',
        maxBuffer: 1024 * 1024 * 10, // 10MB buffer
        timeout: 60000, // 60 segundos timeout
        ...options
    };

    try {
        // No Windows, adiciona configurações específicas para evitar problemas com pipes
        if (isWindows) {
            // Força o uso de cmd no Windows para evitar problemas com pipes
            defaultOptions.shell = 'cmd.exe';
            // Adiciona variáveis de ambiente específicas para Docker no Windows
            defaultOptions.env = {
                ...process.env,
                DOCKER_BUILDKIT: '1',
                COMPOSE_DOCKER_CLI_BUILD: '1'
            };
        }

        return execSync(command, defaultOptions);
    } catch (error) {
        // Tratamento específico para erros comuns do Docker Desktop no Windows
        if (error.message.includes('Internal Server Error') || 
            error.message.includes('pipe/dockerDesktopLinuxEngine')) {
            throw new Error(`Erro do Docker Desktop: ${error.message}\n\nSoluções possíveis:\n1. Reinicie o Docker Desktop\n2. Verifique se o Docker Desktop está rodando como administrador\n3. Tente executar o comando novamente após alguns segundos`);
        }
        throw error;
    }
}

module.exports = { 
    getDockerComposeCommand, 
    normalizeFilePath,
    executeDockerComposeCommand
};
