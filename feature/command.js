const { runFeatureCommand } = require('./feature.js');
const { logSuccess, logError, logInfo } = require('../util/log.js');
const ora = require("ora");
const emoji = require("node-emoji");

function addFeatureCommand(program) {
    program
        .command('feature')
        .alias('f')
        .description('Gerenciar features de uma empresa')
        .action(async () => {
            await runFeatureCommand();
        });
}

module.exports = { addFeatureCommand };
