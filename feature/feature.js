const axios = require("axios");
const inquirer = require("inquirer");
inquirer.registerPrompt(
  "autocomplete",
  require("inquirer-autocomplete-prompt"),
);
const { exec } = require("child_process");
const { getValue, setValue } = require("../util/storage");
const ora = require("ora");
const emoji = require("node-emoji");
const open = require("open");
const { getContainerEnvVariable, findContainerByImage } = require("../deploy/container");

async function findPostgresContainer() {
  return new Promise((resolve, reject) => {
    exec("docker ps", (err, stdout, stderr) => {
      if (err) {
        return reject(`Erro ao executar o comando Docker: ${stderr}`);
      }
  
      const lines = stdout.split("\n").slice(1);
      const postgresContainer = lines.find((line) =>
        line.includes("registry.gitlab.com/plataformazw/docker-pacto/postgres:")
      );
  
      if (postgresContainer) {
        const containerName = postgresContainer.split(" ")[0];
        resolve(containerName);
      } else {
        reject("Nenhum container Postgres encontrado");
      }
    });
  });
}

async function getCompanyKeys() {
  const containerName = await findPostgresContainer();
  if (!containerName) {
    throw new Error("Postgres container not found");
  }

  return new Promise((resolve, reject) => {
    exec(
      `docker exec ${containerName} psql -U postgres -d OAMD -c "select chave, identificadorempresa from empresa where chave not in('testeAmericano');"`,
      (err, stdout, stderr) => {
        if (err) {
          return reject(err);
        }
        
        const lines = stdout.split("\n");
        const dataLines = lines.filter(line => {
          const trimmed = line.trim();
          return trimmed && 
                 !trimmed.startsWith("chave") &&
                 !trimmed.startsWith("-----") &&
                 !trimmed.startsWith("(");
        });
        
        const companies = dataLines.map(line => {
          const parts = line.split("|").map(part => part.trim());
          return {
            chave: parts[0],
            identificadorempresa: parts[1]
          };
        });
        resolve(companies);
      }
    );
  });
}

async function updateCompanyModules(chave, moduleCode, action) {
  const containerName = await findPostgresContainer();
  if (!containerName) {
    throw new Error("Postgres container not found");
  }

  return new Promise((resolve, reject) => {
    exec(
      `docker exec ${containerName} psql -U postgres -d OAMD -c "select modulos from empresa where chave = '${chave}';"`,
      (err, stdout, stderr) => {
        if (err) {
          return reject(err);
        }
        
        const lines = stdout.split("\n");
        const dataLines = lines.filter(line => {
          const trimmed = line.trim();
          return trimmed && 
                 !trimmed.startsWith("modulos") &&
                 !trimmed.startsWith("-----") &&
                 !trimmed.startsWith("(");
        });
        
        let currentModules = dataLines.length > 0 ? dataLines[0].trim() : '';
        let modulesArray = currentModules ? currentModules.split(',').map(m => m.trim()) : [];
        
        if (action === 'habilitar') {
          if (!modulesArray.includes(moduleCode)) {
            modulesArray.push(moduleCode);
          }
        } else if (action === 'desabilitar') {
          modulesArray = modulesArray.filter(m => m !== moduleCode);
        }
        
        const newModules = modulesArray.join(',');
        
        exec(
          `docker exec ${containerName} psql -U postgres -d OAMD -c "UPDATE empresa SET modulos = '${newModules}' WHERE chave = '${chave}';"`,
          (updateErr, updateStdout, updateStderr) => {
            if (updateErr) {
              return reject(updateErr);
            }
            resolve(newModules);
          }
        );
      }
    );
  });
}

async function reloadServiceDiscovery() {
  try {
    const discoveryUrl = "http://host.docker.internal:8101";
    await axios.get(`${discoveryUrl}/reload`);
    return true;
  } catch (error) {
    console.error(`Erro ao fazer reload do service discovery: ${error.message}`);
    return false;
  }
}

async function runFeatureCommand() {
  let spinner = null;
  try {
    const { selectedFeature } = await inquirer.prompt([
      {
        type: "list",
        name: "selectedFeature",
        message: "Selecione a feature:",
        choices: [
          { name: "Conversas.ai", value: { name: "Conversas.ai", code: "IA" } },
          { name: "ZW-BOOT", value: { name: "ZW-BOOT", code: "ZWB" } }
        ]
      }
    ]);

    spinner = ora(`Buscando empresas disponíveis ${emoji.get("hourglass_flowing_sand")}`).start();
    const companies = await getCompanyKeys();
    spinner.stop();

    if (companies.length === 0) {
      console.log("Nenhuma empresa encontrada");
      process.exit(0);
    }

    const { selectedCompany } = await inquirer.prompt([
      {
        type: "list",
        name: "selectedCompany",
        message: "Selecione a empresa:",
        choices: companies.map((company) => ({
          name: `${company.identificadorempresa} - ${company.chave}`,
          value: company.chave,
        })),
        pageSize: 10,
      },
    ]);

    const { selectedAction } = await inquirer.prompt([
      {
        type: "list",
        name: "selectedAction",
        message: `Selecione a ação para ${selectedFeature.name}:`,
        choices: [
          { name: "Habilitar", value: "habilitar" },
          { name: "Desabilitar", value: "desabilitar" }
        ]
      }
    ]);

    spinner = ora(
      `${selectedAction === 'habilitar' ? 'Habilitando' : 'Desabilitando'} ${selectedFeature.name} para a empresa ${selectedCompany} ${emoji.get("hourglass_flowing_sand")}`
    ).start();

    await updateCompanyModules(selectedCompany, selectedFeature.code, selectedAction);
    
    spinner.text = `Fazendo reload do service discovery ${emoji.get("hourglass_flowing_sand")}`;
    const reloadSuccess = await reloadServiceDiscovery();
    
    if (reloadSuccess) {
      spinner.succeed(
        `${selectedFeature.name} ${selectedAction === 'habilitar' ? 'habilitada' : 'desabilitada'} com sucesso! ${emoji.get("white_check_mark")}`
      );
    } else {
      spinner.warn(
        `${selectedFeature.name} ${selectedAction === 'habilitar' ? 'habilitada' : 'desabilitada'}, mas houve erro no reload do service discovery ${emoji.get("warning")}`
      );
    }

    const zwContainer = await findContainerByImage("registry.gitlab.com/plataformazw/zw/tomcat:");
    const urlNovoLogin = await getContainerEnvVariable(zwContainer, "URL_NOVO_LOGIN");
    const loginUrl = `${urlNovoLogin}/pt/auth`;
    console.log(`\n🔗 Abrindo URL de login: ${loginUrl}`);
    console.log(`\n⚠️  Você precisa fazer login novamente para carregar as novas configurações.`);
    open(loginUrl);

  } catch (error) {
    if (spinner) {
      spinner.fail(
        `Erro ao gerenciar feature: ${error.message} ${emoji.get("x")}`
      );
    }

    console.error(`Erro ao executar o comando de feature: ${error.message}`);
    process.exit(1);
  }
}

module.exports = { runFeatureCommand };
