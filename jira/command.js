const inquirer = require('inquirer');
const {configureJiraCredentials, getJiraCredentials} = require("./credentials");
const {searchIssues, getIssueWithWeblinks} = require("./service");
const { getValue, setValue } = require('../util/storage.js');
const { logSuccess, logError, logInfo, logJira } = require('../util/log.js');

module.exports.addJiraCommands = (program) => {
    const jira = program
        .command('jira')
        .alias('j')
        .description('Configuração e integração com o Jira');

    jira
        .command('credenciais')
        .alias('c')
        .description('Configura as credenciais do Jira')
        .action(async () => {
            await configureJiraCredentials();
        });

    jira
        .command('ajuda')
        .alias('a')
        .description('Exibe o link para obter o token do Jira')
        .action(() => {
            logInfo('🔗 Acesse o link para gerar seu token:');
            logJira('🎯 https://id.atlassian.com/manage-profile/security/api-tokens');
        });

    jira.command('config-busca')
        .alias('cb')
        .description('Configura os filtros de busca do Jira')
        .action(async () => {
            const jiraFilters = getValue('jira.filters');
            const savedFilters = (jiraFilters && jiraFilters.filters) ? jiraFilters.filters : [];

            // Definir opções de filtros
            const filters = [
                { name: 'Retorna todas as issues onde você é o repórter (a pessoa que criou a issue).', value: 'reporter = currentUser()' },
                { name: 'Retorna todas as issues onde você é o responsável pela tarefa.', value: 'assignee = currentUser()' },
                { name: 'Retorna todas as issues onde você é um observador (watcher). Observadores recebem notificações sobre atualizações da issue.', value: 'watcher = currentUser()' },
            ];

            // Exibir filtros com os já selecionados
            const { selectedFilters } = await inquirer.prompt([
                {
                    type: 'checkbox',
                    name: 'selectedFilters',
                    message: 'Selecione os filtros para a busca:',
                    choices: filters.map((filter) => ({
                        name: filter.name,
                        value: filter.value,
                        checked: savedFilters.includes(filter.value),
                    })),
                },
            ]);

            if (selectedFilters.length === 0) {
                console.log('Nenhum filtro selecionado.');
                return;
            }

            const configExcludedDone = getValue('jira.excludeDone');
            const { excludeDone } = await inquirer.prompt([
                {
                    type: 'confirm',
                    name: 'excludeDone',
                    message: 'Deseja excluir atividades finalizadas (AND statusCategory != Done)?',
                    default: configExcludedDone || false,
                },
            ]);

            const configOrderBy = getValue('jira.orderBy');
            const customOrder = await inquirer.prompt([
                {
                    type: 'input',
                    name: 'orderBy',
                    message: 'Deseja adicionar uma ordenação? (ex: ORDER BY updated DESC)',
                    default: configOrderBy || 'ORDER BY updated DESC',
                },
            ]);

            // Atualizar e salvar configuração
            const newConfig = {
                ...savedFilters,
                filters: selectedFilters,
                excludeDone,
                orderBy: customOrder.orderBy,
                jql: `(${selectedFilters.join(' OR ')})${excludeDone ? ' AND statusCategory != Done' : ''} ${customOrder.orderBy}`,
            };

            setValue('jira.filters', newConfig);

            logSuccess('🎉 Configuração de busca salva com sucesso!');
        });

    jira.command('busca')
        .alias('b')
        .description('Busca atividades no Jira')
        .action(async () => {
            const config = getJiraCredentials();
            if (!config.token || !config.email) {
                logError('🔍💥 N Nenhuma configuração de busca encontrada. Use "pacto jira config-busca" para configurar.');
                return;
            }

            logJira(`🔍 Usando o filtro: ${config.jql}`);
            const issues = await searchIssues(config.jql);
            if (issues.length === 0) {
                logInfo('🤷‍♂️ Nenhuma atividade encontrada. Que silêncio...');
                return;
            }


            const { selectedIssue } = await inquirer.prompt([
                {
                    type: 'autocomplete',
                    name: 'selectedIssue',
                    message: 'Selecione uma atividade:',
                    source: (answersSoFar, input) => {
                        input = input || '';
                        return new Promise((resolve) => {
                            const filteredIssues = issues.filter((issue) =>
                                issue.fields.summary.toLowerCase().includes(input.toLowerCase()) ||
                                issue.key.toLowerCase().includes(input.toLowerCase())
                            );
                            resolve(filteredIssues.map((issue) => ({
                                name: `${issue.key} -> ${issue.fields.summary}`,
                                value: issue.key,
                            })));
                        });
                    },
                },
            ]);

            console.log(`Atividade selecionada: ${selectedIssue}`);
        });

    jira.command('weblinks')
        .alias('w')
        .description('Consulta os weblinks associados a uma issue do Jira')
        .action(async () => {
            const config = getValue('jira.filters');
            if (!config.jql) {
                console.error('Nenhuma configuração de busca encontrada. Use "pacto jira config-busca" para configurar.');
                return;
            }

            console.log(`Usando o filtro: ${config.jql}`);
            const issues = await searchIssues(config.jql);
            if (issues.length === 0) {
                console.log('Nenhuma atividade encontrada.');
                return;
            }

            const {selectedIssue} = await inquirer.prompt([
                {
                    type: 'autocomplete',
                    name: 'selectedIssue',
                    message: 'Selecione uma atividade para ver os weblinks:',
                    source: (answersSoFar, input) => {
                        input = input || '';
                        return new Promise((resolve) => {
                            const filteredIssues = issues.filter((issue) =>
                                issue.fields.summary.toLowerCase().includes(input.toLowerCase()) ||
                                issue.key.toLowerCase().includes(input.toLowerCase())
                            );
                            resolve(filteredIssues.map((issue) => ({
                                name: `${issue.key} -> ${issue.fields.summary}`,
                                value: issue.key,
                            })));
                        });
                    },
                },
            ]);

            // Get the issue details with weblinks
            try {
                const issueDetails = await getIssueWithWeblinks(selectedIssue);
                
                // Check if there are any weblinks
                if (!issueDetails.fields.issuelinks || issueDetails.fields.issuelinks.length === 0) {
                    console.log(`Nenhum weblink encontrado para a issue ${selectedIssue}.`);
                    return;
                }
                
                console.log(`\nWeblinks para a issue ${selectedIssue}:`);
                issueDetails.fields.issuelinks.forEach((link, index) => {
                    if (link.object && link.object.url) {
                        console.log(`${index + 1}. ${link.object.title || 'Link sem título'}: ${link.object.url}`);
                    }
                });
            } catch (error) {
                console.error(`Erro ao buscar weblinks para a issue ${selectedIssue}:`, error.message);
            }
        });

};