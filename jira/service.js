const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { getValue } = require('../util/storage');

async function searchIssues(jql, fields = 'key,summary') {
    const email = getValue('jira.email');
    const token = getValue('jira.token');
    const jiraBaseUrl = 'https://pacto.atlassian.net/rest/api/3/search';

    try {
        const response = await axios.get(jiraBaseUrl, {
            headers: {
                Authorization: `Basic ${Buffer.from(`${email}:${token}`).toString('base64')}`,
                'Content-Type': 'application/json',
            },
            params: {
                jql,
                fields,
            },
        });
        // console.log(response)
        return response.data.issues;
    } catch (error) {
        console.error('Erro ao buscar atividades no Jira:', error.response.data || error.message);
        return [];
    }
}


async function getIssueWithWeblinks(issueKey) {
    const email = getValue('jira.email');
    const token = getValue('jira.token');
    const baseUrl = 'https://pacto.atlassian.net/rest/api/3';

    try {
        // Get the remote links (web links)
        const remoteLinksResponse = await axios.get(
            `${baseUrl}/issue/${issueKey}/remotelink`,
            {
                headers: {
                    Authorization: `Basic ${Buffer.from(`${email}:${token}`).toString('base64')}`,
                    'Content-Type': 'application/json',
                }
            }
        );
        
        // Extract just the title and url from each remote link
        const webLinks = remoteLinksResponse.data.map(link => {
            if (link.object) {
                return {
                    title: link.object.title || 'No Title',
                    url: link.object.url || ''
                };
            }
            return null;
        }).filter(link => link !== null);
        
        return webLinks;
    } catch (error) {
        console.error('Erro ao buscar weblinks da issue:', error.response.data || error.message);
        throw new Error(`Erro ao buscar weblinks da issue: ${error.message}`);
    }
}


module.exports = {searchIssues, getIssueWithWeblinks};