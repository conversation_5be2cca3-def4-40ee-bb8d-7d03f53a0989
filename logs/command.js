const inquirer = require('inquirer');
const axios = require('axios');
const open = require('open');
const { log, logSuccess, logError, logInfo } = require('../util/log.js');
const { getGitlabToken, getGitlabTokenExportLogs } = require('../git/gitlab.js');
const { getValue, setValue } = require('../util/storage.js');
const { get } = require('node-emoji/lib/emoji.js');

/**
 * Execute the request to trigger the logs pipeline using axios
 * @param {string} zone The zone number
 * @param {string} tail The path to tail logs from
 */
const executeCurlCommand = async (zone, tail) => {
  if (!zone) {
    console.error('Param zone is required.');
    process.exit(2);
  }

  try {
    let token = await getGitlabTokenExportLogs();
    if(!token){
      token = await getGitlabToken();
    }
    const url = 'https://gitlab.com/api/v4/projects/25948734/trigger/pipeline';

    log('pacto:logs', `Triggering logs pipeline for zone ${zone} with path ${tail}`);

    const formData = new URLSearchParams();
    formData.append('token', token);
    formData.append('variables[ZONA]', zone);
    formData.append('variables[TAIL]', tail);
    formData.append('ref', 'master');

    const response = await axios.post(url, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    if (response.status >= 200 && response.status < 300) {
      logSuccess(`🚀 Pipeline de logs disparado com sucesso! Que show!`);
      open('https://gitlab.com/wallerpacto/export-logs');
    } else {
      logError(`💥 Erro: Recebido status code ${response.status}`);
      logError('📄 Detalhes do erro:', response.data);
    }
  } catch (error) {
    logError('💥 Erro ao disparar pipeline de logs:');
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      logError(`📊 Status: ${error.response.status}`);
      logError('📄 Dados da resposta:', JSON.stringify(error.response.data));
    } else if (error.request) {
      // The request was made but no response was received
      logError('📡 Nenhuma resposta recebida do servidor. Será que ele foi tomar café?');
    } else {
      // Something happened in setting up the request that triggered an Error
      logError(`💥 Erro: ${error.message}`);
    }
  }
};

/**
 * Handle PostgreSQL logs request
 * @param {string} zone The zone number
 */
const handlePostgres = (zone) => {
  const tail = '/var/lib/postgresql/9.3/main/pg_log/*.log';
  executeCurlCommand(zone, tail);
};

/**
 * Handle Zillyon Web logs request
 * @param {string} zone The zone number
 */
const handleZillyonWeb = (zone) => {
  const tail = '/opt/glassfish4/glassfish/nodes/*/zw-01/logs/server.log*';
  executeCurlCommand(zone, tail);
};

/**
 * Handle Treino logs request
 * @param {string} zone The zone number
 */
const handleTreino = (zone) => {
  const tail = '/opt/glassfish4/glassfish/nodes/*/tr-*/logs/server.log*';
  executeCurlCommand(zone, tail);
};

/**
 * Handle microservice logs request
 * @param {string} zone The zone number
 * @param {string} msName The microservice name
 */
const handleMicroservice = (zone, msName) => {
  const tail = `/var/log/${msName}.log`;
  executeCurlCommand(zone, tail);
};

/**
 * Add logs commands to the program
 * @param {object} program The commander program object
 */
function addLogsCommands(program) {
  const logsCommand = program
    .command('logs')
    .alias('l')
    .description('Gerenciar logs dos ambientes')
    .action(async () => {
      const defaultZone = getValue('defaultLogsZone');
      // Ask for the zone
      const { zone } = await inquirer.prompt([
        {
          type: 'input',
          name: 'zone',
          message: 'Zona (ex: 302):',
          default: defaultZone,
          validate: (input) => {
            if (!input) {
              return 'A zona é obrigatória';
            }
            return true;
          }
        }
      ]);

      setValue('defaultLogsZone', zone);

      const defaultApplicationType = getValue('defaultLogsApplicationType');

      // Ask for application type
      const { applicationType } = await inquirer.prompt([
        {
          type: 'list',
          name: 'applicationType',
          message: 'Escolha o tipo de aplicação:',
          default: defaultApplicationType,
          choices: [
            { name: 'Postgres', value: 'postgres' },
            { name: 'Aplicações', value: 'applications' }
          ]
        }
      ]);

      setValue('defaultLogsApplicationType', applicationType);

      if (applicationType === 'postgres') {
        handlePostgres(zone);
      } else {
        // Ask which application

        const defaultLogsApplication = getValue('defaultLogsApplication');
        const { application } = await inquirer.prompt([
          {
            type: 'list',
            name: 'application',
            message: 'Escolha a aplicação:',
            default: defaultLogsApplication,
            choices: [
              { name: 'Zillyon Web', value: 'zillyon-web' },
              { name: 'Treino', value: 'treino' },
              { name: 'Adm-ms', value: 'adm-ms' },
              { name: 'Crm-ms', value: 'crm-ms' },
              { name: 'Plano-ms', value: 'plano-ms' },
              { name: 'Adm-core-ms', value: 'adm-core-ms' },
              { name: 'Financeiro-ms', value: 'financeiro-ms' }
            ]
          }
        ]);

        setValue('defaultLogsApplication', application);

        // Process the selected application
        switch (application) {
          case 'zillyon-web':
            handleZillyonWeb(zone);
            break;
          case 'treino':
            handleTreino(zone);
            break;
          case 'adm-ms':
            handleMicroservice(zone, 'adm-ms');
            break;
          case 'crm-ms':
            handleMicroservice(zone, 'crm-ms');
            break;
          case 'plano-ms':
            handleMicroservice(zone, 'plano-ms');
            break;
          case 'adm-core-ms':
            handleMicroservice(zone, 'adm-core-ms');
            break;
          case 'financeiro-ms':
            handleMicroservice(zone, 'financeiro-ms');
            break;
          default:
            logError('🤔 Aplicação não reconhecida. Será que ela existe mesmo?');
            break;
        }
      }
    });
}

module.exports = { addLogsCommands };

