const {exec} = require('child_process');
const {execSync} = require('child_process');
const inquirer = require("inquirer");
inquirer.registerPrompt('checkbox-plus', require('inquirer-checkbox-plus-prompt'));

const open = require("open");
const {
    getComposeServiceGoupNames,
    getComposeServicesGroupByName,
    getComposeServices,
    changeComposeServiceTags,
    updateComposeServiceTags,
    getServiceNameByGitlabMergeRequestUrl,
    changeComposeMultipleServiceTags,
    getPredefinedVersionBranches,
    getPredefinedVersionServices,
    updateComposeRemovePorts
} = require("./compose/services.js");
const {deployCompose, resetBranchs, getComposePath} = require("./compose/deploy.js");
const {getDockerComposeCommand} = require("../util/docker-compose.js");

const {getUrlsFromDockerCompose} = require('./compose/env.js');
const {normalizeBranchName} = require('../git/branch');
const {searchIssues, getIssueWithWeblinks} = require("../jira/service");
const {getGitlabToken, getMergeRequestBranches} = require("../git/gitlab.js")

const {getValue, setValue} = require('../util/storage.js');
const {waitStartContainers} = require('./container.js');
const {dockerLoginGitlab} = require('../util/docker.js');
const {logSuccess, logError, logWarning, logInfo, logProcess, logContainer, logDeploy, logFun, logMagic} = require('../util/log.js');

async function getRunningContainers() {
    try {
        const composeCommand = getDockerComposeCommand();
        const composePath = getComposePath();
        const containersList = execSync(composeCommand + ' -p dev-tools -f "' + composePath + '" config --services').toString().split('\n').filter(Boolean);
        return containersList;
    } catch (error) {
        logError('Erro ao obter a lista de containers:', error);
        return [];
    }
}

async function addDeployCommands(program) {
    const d = program
        .command('deploy')
        .alias('d')
        .description('Realiza deploy de aplicações pacto em ambiente de desenvolvimento e teste');

    d.command('review')
        .alias('r')
        .description('Revisa todas as aplicações deployadas com o prefixo dev-tools')
        .action(() => {
            exec('docker ps --filter "name=dev-tools"', (err, stdout, stderr) => {
                if (err) {
                    logError(`Erro ao revisar as aplicações deployadas: ${err}`);
                    return;
                }
                logContainer('Aplicações deployadas:', stdout);
                if (stderr) {
                    logWarning('Avisos:', stderr);
                }
            });
        });


    d.command('open-url')
        .alias('u')
        .description('Abre a URL dos serviços do Docker Compose')
        .action(async () => {
            const urls = getUrlsFromDockerCompose();
            const questions = [
                {
                    type: 'autocomplete',
                    name: 'selected_url',
                    message: 'Selecione a URL do serviço para abrir:',
                    source: (answersSoFar, input) => {
                        input = input || '';
                        return new Promise((resolve) => {
                            const filteredUrls = urls.filter(url => url.name.toLowerCase().includes(input.toLowerCase()));
                            resolve(filteredUrls.map(url => {
                                let formatedUrl = url.value;
                                if (url.name === 'URL_NOVO_LOGIN') {
                                    formatedUrl = `${url.value}/pt/auth`;
                                }
                                return {name: url.name, value: formatedUrl};
                            }));

                        });
                    }
                }
            ];

            inquirer.prompt(questions).then((answers) => {
                open(answers.selected_url);
            });
        });

    d.command('remove')
        .alias('rm')
        .description('Remove todas as aplicações deployadas com o prefixo dev-tools')
        .action(() => {
            exec('docker ps -a --filter "name=dev-tools" --format "{{.ID}}"', (err, stdout, stderr) => {
                if (err) {
                    logError(`Erro ao listar os containers: ${err}`);
                    return;
                }
                const containerIds = stdout.split('\n').filter(id => id);
                if (containerIds.length > 0) {
                    logProcess(`🧹 Fazendo faxina! Removendo ${containerIds.length} containers que estavam fazendo bagunça...`);
                    exec(`docker rm -f ${containerIds.join(' ')}`, (err, stdout, stderr) => {
                        if (err) {
                            logError(`Erro ao remover os containers: ${err}`);
                            return;
                        }
                        logSuccess(`Containers removidos com sucesso!`, stdout);
                        if (stderr) {
                            logWarning('Avisos durante remoção:', stderr);
                        }
                    });
                } else {
                    logInfo('Nenhum container deployado por dev-tools encontrado.');
                }
            });
        });

    d.command('stop')
        .alias('st')
        .description('Para todas as aplicações deployadas com o prefixo dev-tools')
        .action(async () => {
            try {
                // Obter a lista de containers em execução
                const containersList = await getRunningContainers();

                if (containersList.length === 0) {
                    logInfo('Nenhum container dev-tools em execução no momento.');
                    return;
                }

                const {stopOption} = await inquirer.prompt([
                    {
                        type: 'list',
                        name: 'stopOption',
                        message: 'Como deseja parar os containers?',
                        choices: ['Selecionar serviços', 'Parar stack', 'Parar todos'],
                        default: 'Selecionar serviços',
                    }
                ]);

                if (stopOption === 'Selecionar serviços') {
                    const {selectedContainers} = await inquirer.prompt([
                        {
                            type: 'checkbox-plus',
                            name: 'selectedContainers',
                            message: 'Selecione os containers para parar:',
                            pageSize: 10,
                            highlight: true,
                            searchable: true,
                            validate: (answer) => {
                                if (answer.length < 1) {
                                    return 'Selecione pelo menos um container.';
                                }
                                return true;
                            },
                            source: (answersSoFar, input) => {
                                input = input || '';
                                return new Promise((resolve) => {
                                    const filteredContainers = containersList.filter(container =>
                                        container.toLowerCase().includes(input.toLowerCase())
                                    );
                                    resolve(filteredContainers.map(container => ({
                                        name: container,
                                        value: container
                                    })));
                                });
                            }
                        }
                    ]);

                    // Parar os containers selecionados usando docker-compose
                    const composeCommand = getDockerComposeCommand();
                    logContainer(`🛑 Parando containers selecionados: ${selectedContainers.join(', ')}`);
                    execSync(`${composeCommand} -p dev-tools stop ${selectedContainers.join(' ')}`, {stdio: 'inherit'});
                    logSuccess('🎯 Containers parados com sucesso! Agora estão dormindo...');
                } else if (stopOption === 'Parar stack') {
                    // Nova opção para parar uma stack de serviços
                    const {selectedStack} = await inquirer.prompt([
                        {
                            type: 'list',
                            name: 'selectedStack',
                            message: 'Selecione a stack para parar:',
                            choices: getComposeServiceGoupNames(),
                        }
                    ]);
                    
                    const stackServices = getComposeServicesGroupByName(selectedStack);
                    if (stackServices && stackServices.length > 0) {
                        // Filtrar apenas os serviços que estão em execução
                        const runningStackServices = stackServices.filter(service => 
                            containersList.some(container => container.includes(service))
                        );
                        
                        if (runningStackServices.length > 0) {
                            const composeCommand = getDockerComposeCommand();
                            logContainer(`🛑 Parando stack ${selectedStack}: ${runningStackServices.join(', ')}`);
                            execSync(`${composeCommand} -p dev-tools stop ${runningStackServices.join(' ')}`, {stdio: 'inherit'});
                            logSuccess(`🎯 Stack ${selectedStack} parada com sucesso! Todos dormindo...`);
                        } else {
                            logInfo(`😴 Nenhum serviço da stack ${selectedStack} está em execução. Já estão dormindo!`);
                        }
                    } else {
                        logWarning('🤔 Nenhum serviço encontrado para esta stack. Será que existe mesmo?');
                    }
                } else {
                    // Parar todos os containers dev-tools
                    logContainer('🛑 Parando todos os containers dev-tools... Hora da soneca geral!');
                    exec('docker ps --filter "name=dev-tools" --format "{{.ID}}"', (err, stdout, stderr) => {
                        if (err) {
                            logError(`💥 Erro ao listar os containers: ${err}`);
                            return;
                        }
                        const containerIds = stdout.split('\n').filter(id => id);
                        if (containerIds.length > 0) {
                            exec(`docker stop ${containerIds.join(' ')}`, (err, stdout, stderr) => {
                                if (err) {
                                    logError(`💥 Erro ao parar os containers: ${err}`);
                                    return;
                                }
                                logSuccess(`🎯 Containers parados: ${stdout}`);
                                if (stderr) {
                                    logWarning(`⚠️ Avisos: ${stderr}`);
                                }
                            });
                        } else {
                            logInfo('😴 Nenhum container deployado por dev-tools encontrado. Já estão todos dormindo!');
                        }
                    });
                }
            } catch (error) {
                logError('💥 Ops! Algo deu errado ao parar os containers:', error);
            }
        });

    d.command('restart')
        .alias('rs')
        .description('Reinicia containers selecionados ou todos os containers dev-tools')
        .action(async () => {
            try {
                const containersList = await getRunningContainers();

                const {restartOption} = await inquirer.prompt([
                    {
                        type: 'list',
                        name: 'restartOption',
                        message: 'Como deseja reiniciar os containers?',
                        choices: ['Selecionar serviços', 'Reiniciar todos'],
                        default: 'Selecionar serviços',
                    }
                ]);

                let selectedLogContainers = []; 

                if (restartOption === 'Selecionar serviços') {
                    const {selectedContainers} = await inquirer.prompt([
                        {
                            type: 'checkbox-plus',
                            name: 'selectedContainers',
                            message: 'Selecione os containers para reiniciar:',
                            pageSize: 10,
                            highlight: true,
                            searchable: true,
                            validate: (answer) => {
                                if (answer.length < 1) {
                                    return 'Selecione pelo menos um container.';
                                }
                                return true;
                            },
                            source: (answersSoFar, input) => {
                                input = input || '';
                                return new Promise((resolve) => {
                                    const filteredContainers = containersList.filter(container =>
                                        container.toLowerCase().includes(input.toLowerCase())
                                    );
                                    resolve(filteredContainers.map(container => ({
                                        name: container,
                                        value: container
                                    })));
                                });
                            }
                        }
                    ]);
                    selectedLogContainers = selectedContainers;
                    const composeCommand = getDockerComposeCommand();
                    execSync(`${composeCommand} -p dev-tools restart ${selectedContainers.join(' ')}`, {stdio: 'inherit'});
                } else {
                    const composeCommand = getDockerComposeCommand();
                    execSync(`${composeCommand} -p dev-tools restart`, {stdio: 'inherit'});
                }

                logSuccess('🔄 Containers reiniciados com sucesso! Agora estão fresquinhos!');
                const showLogsDefault = getValue('deploy:show_logs_after_restart') || true;
                const {showLogs} = await inquirer.prompt([
                    {
                        type: 'confirm',
                        name: 'showLogs',
                        message: 'Deseja ver os logs dos containers reiniciados?',
                        default: showLogsDefault
                    }
                ]);

                if (showLogs) {
                    let containersToLog = [];
                    if (restartOption === 'Selecionar serviços') {
                        containersToLog = selectedLogContainers;
                    } else {
                        containersToLog = containersList;
                    }
                    if (containersToLog.length > 0) {
                        const composeCommand = getDockerComposeCommand();
                        const logsCommand = `${composeCommand} -p dev-tools logs -f --tail=100 ${containersToLog.join(' ')}`;
                        logContainer('🎬 Hora do show! Exibindo logs dos containers:', containersToLog.join(', '));
                        execSync(logsCommand, {stdio: 'inherit'});
                    } else {
                        logInfo('🤷‍♂️ Nenhum container para mostrar logs. Que silêncio...');
                    }
                }
            } catch (error) {
                logError('💥 Ops! Algo deu errado ao reiniciar os containers:', error);
            }
        });


    // Comando principal de deploy (anteriormente era subcomando compose)
    d.option('--stack <stack>', 'Nome da stack predefinida')
        .option('--services <services>', 'Lista de serviços separados por vírgula')
        .option('--reset-branches [boolean]', 'Resetar branches para master')
        .option('--update-services [boolean]', 'Atualizar serviços')
        .option('--branches <branches>', 'Lista de branches para serviços no formato serviço=branch,serviço2=branch2')
        .option('--disable-ports <ports>', 'Ports que serão desabilitadas no formato 11211,15672')
        .action(async (cmdOptions) => {
            const services = getComposeServices();
            await getGitlabToken();
            const defaultDeployOption = getValue('deploy:default_deploy_option');
            const defaultSelectedStack = getValue('deploy:selected_stack');
            const defaultSelectedServices = getValue('deploy:default_selected_services');
            const defaultResetBranchs = getValue('deploy:default_reset_branchs');
            const defaultUpdateBranchs = getValue('deploy:default_update_branchs');

            // Verificar se os parâmetros foram fornecidos via linha de comando
            let selectedServices;
            let resetBranches;
            let updateServices;

            // Se stack ou services foram fornecidos via CLI, não precisamos perguntar deploy_option
            const deployOptionProvided = cmdOptions.stack || cmdOptions.services;

            // Preparar perguntas que serão feitas apenas se os parâmetros não foram fornecidos via CLI
            const questions = [];
            const disablePorts = cmdOptions.disablePorts ? cmdOptions.disablePorts.split(',') : false;

            if (cmdOptions.branches) {
                const branchesMap = {};
                cmdOptions.branches.split(',').forEach(pair => {
                    const [service, branch] = pair.split('=');
                    if (service && branch) {
                        branchesMap[service.trim()] = branch.trim();
                    }
                });

                if (Object.keys(branchesMap).length > 0) {
                    const branchUpdates = [];
                    Object.entries(branchesMap).forEach(([service, branch]) => {
                        const normalizedBranch = normalizeBranchName(branch);
                        branchUpdates.push({
                            sourceBranch: branch,
                            tagName: normalizedBranch,
                            serviceName: service
                        });
                    });
                    changeComposeMultipleServiceTags(branchUpdates);
                }
            }

            if (!deployOptionProvided) {
                questions.push({
                    type: 'list',
                    name: 'deploy_option',
                    message: 'Você quer selecionar os serviços ou utilizar uma stack predefinida?',
                    choices: ['Utilizar stack predefinida', 'Selecionar serviços'],
                    default: defaultDeployOption
                });
            }

            if (!cmdOptions.stack && !cmdOptions.services) {
                questions.push({
                    type: 'list',
                    name: 'selected_stack',
                    message: 'Selecione a stack predefinida:',
                    default: defaultSelectedStack,
                    choices: getComposeServiceGoupNames(),
                    when: (answers) => !deployOptionProvided && answers.deploy_option === 'Utilizar stack predefinida'
                });

                questions.push({
                    type: 'checkbox-plus',
                    name: 'selected_services',
                    message: 'Selecione os serviços para deploy:',
                    pageSize: 10,
                    highlight: true,
                    searchable: true,
                    default: defaultSelectedServices,
                    validate: (answer) => {
                        if (answer.length < 1) {
                            return 'Você deve selecionar pelo menos um serviço.';
                        }
                        return true;
                    },
                    source: (answersSoFar, input) => {
                        input = input || '';
                        return new Promise((resolve) => {
                            const filteredServices = services.filter(service => service.toLowerCase().includes(input.toLowerCase()));
                            resolve(filteredServices.map(service => ({name: service, value: service})));
                        });
                    },
                    when: (answers) => !deployOptionProvided && answers.deploy_option === 'Selecionar serviços'
                });
            }

            if (cmdOptions.resetBranches === undefined && cmdOptions.branches === undefined) {
                questions.push({
                    type: 'confirm',
                    name: 'reset_branchs',
                    message: 'Você quer resetar as branches dos serviços para branchs master?',
                    default: (defaultResetBranchs === false) ? false : true
                });
            }

            if (cmdOptions.updateServices === undefined) {
                questions.push({
                    type: 'confirm',
                    name: 'update_services',
                    message: 'Você quer atualizar os serviços?',
                    default: (defaultUpdateBranchs === false) ? false : true
                });
            }

            // Se temos perguntas para fazer, usamos inquirer, caso contrário processamos diretamente
            if (questions.length > 0) {
                inquirer.prompt(questions).then(async (answers) => {
                    // Determinar os serviços selecionados com base nos parâmetros CLI ou respostas do inquirer
                    if (cmdOptions.stack) {
                        const selectedGroup = getComposeServicesGroupByName(cmdOptions.stack);
                        selectedServices = selectedGroup.join(' ');
                    } else if (cmdOptions.services) {
                        selectedServices = cmdOptions.services.split(',').join(' ');
                    } else if (answers.deploy_option === 'Utilizar stack predefinida') {
                        const selectedGroup = getComposeServicesGroupByName(answers.selected_stack);
                        selectedServices = selectedGroup.join(' ');
                    } else {
                        selectedServices = answers.selected_services.join(' ');
                    }

                    // Determinar se devemos resetar branches
                    resetBranches = cmdOptions.resetBranches !== undefined ?
                        cmdOptions.resetBranches === 'true' || cmdOptions.resetBranches === true :
                        answers.reset_branchs;

                    // Determinar se devemos atualizar serviços
                    updateServices = cmdOptions.updateServices !== undefined ?
                        cmdOptions.updateServices === 'true' || cmdOptions.updateServices === true :
                        answers.update_services;

                    // Salvar preferências
                    setValue('deploy:default_deploy_option', answers.deploy_option || (cmdOptions.stack ? 'Utilizar stack predefinida' : 'Selecionar serviços'));
                    setValue('deploy:selected_stack', answers.selected_stack || cmdOptions.stack);
                    setValue('deploy:default_selected_services', answers.selected_services || (cmdOptions.services ? cmdOptions.services.split(',') : []));
                    setValue('deploy:default_reset_branchs', resetBranches);
                    setValue('deploy:default_update_branchs', updateServices);

                    try {

                        await dockerLoginGitlab();

                        if (resetBranches) {
                            resetBranchs();
                        }
                        if (updateServices) {
                            await updateComposeServiceTags(selectedServices);
                        }
                        if (disablePorts) {
                            await updateComposeRemovePorts(disablePorts);
                        }

                        await deployCompose(selectedServices);
                        await waitStartContainers();
                    } catch (err) {
                        logError(`🐳💥 Docker Compose teve um chilique: ${err}`);
                    }
                });
            } else {
                // Processar diretamente sem inquirer
                if (cmdOptions.stack) {
                    const selectedGroup = getComposeServicesGroupByName(cmdOptions.stack);
                    selectedServices = selectedGroup.join(' ');
                } else if (cmdOptions.services) {
                    selectedServices = cmdOptions.services.split(',').join(' ');
                }

                resetBranches = cmdOptions.resetBranches === 'true' || cmdOptions.resetBranches === true;
                updateServices = cmdOptions.updateServices === 'true' || cmdOptions.updateServices === true;

                // Salvar preferências
                setValue('deploy:default_deploy_option', cmdOptions.stack ? 'Utilizar stack predefinida' : 'Selecionar serviços');
                setValue('deploy:selected_stack', cmdOptions.stack);
                setValue('deploy:default_selected_services', cmdOptions.services ? cmdOptions.services.split(',') : []);
                setValue('deploy:default_reset_branchs', resetBranches);
                setValue('deploy:default_update_branchs', updateServices);

                // Executar o deploy
                try {

                    await dockerLoginGitlab();

                    if (resetBranches) {
                        resetBranchs();
                    }

                    if (updateServices) {
                        await updateComposeServiceTags(selectedServices);
                    }

                    if (disablePorts) {
                        updateComposeRemovePorts(disablePorts);
                    }

                    await deployCompose(selectedServices);

                    await waitStartContainers();
                } catch (err) {
                    logError(`🐳💥 Docker Compose teve um chilique: ${err}`);
                }
            }
        });

    d.command('branch')
        .alias('b')
        .description('Altera branches de containers em execução')
        .action(async () => {
            try {

                // Perguntar como deseja informar a branch
                const {branchOption} = await inquirer.prompt([
                    {
                        type: 'list',
                        name: 'branchOption',
                        message: 'Informe a branch:',
                        choices: ['Buscar no Jira - Automatizado', 'Buscar no Jira', 'Inserir manualmente', 'Versões'],
                    },
                ]);

                let branchName;
                if (branchOption === 'Buscar no Jira') {
                    const services = await getComposeServices();

                    const {selected_services} = await inquirer.prompt([
                        {
                            type: 'checkbox-plus',
                            name: 'selected_services',
                            message: 'Selecione os serviços para alterar a branch:',
                            pageSize: 10,
                            highlight: true,
                            searchable: true,
                            validate: (answer) => {
                                if (answer.length < 1) {
                                    return 'Você deve selecionar pelo menos um serviço.';
                                }
                                return true;
                            },
                            source: (answersSoFar, input) => {
                                input = input || '';
                                return new Promise((resolve) => {
                                    const filteredServices = services.filter((service) =>
                                        service.toLowerCase().includes(input.toLowerCase())
                                    );
                                    resolve(filteredServices.map((service) => ({name: service, value: service})));
                                });
                            },
                        },
                    ]);

                    const jiraEmail = getValue('jira.email');
                    const jiraToken = getValue('jira.token');
                    if (jiraEmail === undefined || jiraToken === undefined) {
                        logError('📋💥 Configuração do Jira não encontrada. Use "pacto jira config-busca" para configurar.');
                        return;
                    }

                    const jiraFilters = getValue('jira.filters');
                    if (jiraFilters === undefined || jiraFilters.jql === undefined) {
                        logError('🔍💥 Nenhuma configuração de busca encontrada. Use "pacto jira config-busca" para configurar.');
                        return;
                    }

                    logInfo(`🔍 Realizando busca no Jira com o filtro: ${jiraFilters.jql}`);
                    const issues = await searchIssues(jiraFilters.jql);
                    if (issues.length === 0) {
                        logInfo('🤷‍♂️ Nenhuma atividade encontrada no Jira. Que silêncio...');
                        return;
                    }

                    const {selectedIssue} = await inquirer.prompt([
                        {
                            type: 'autocomplete',
                            name: 'selectedIssue',
                            message: 'Selecione uma atividade:',
                            source: (answersSoFar, input) => {
                                input = input || '';
                                return new Promise((resolve) => {
                                    const filteredIssues = issues.filter(issue =>
                                        issue.fields.summary.toLowerCase().includes(input.toLowerCase()) ||
                                        issue.key.toLowerCase().includes(input.toLowerCase())
                                    );
                                    resolve(filteredIssues.map(issue => ({
                                        name: `${issue.key} -> ${issue.fields.summary}`,
                                        value: issue.key,
                                    })));
                                });
                            },
                        },
                    ]);

                    // Oferecer opções para branch
                    const {branchChoice} = await inquirer.prompt([
                        {
                            type: 'list',
                            name: 'branchChoice',
                            message: 'Como deseja configurar a branch?',
                            choices: [
                                {name: `feature/${selectedIssue}`, value: `feature/${selectedIssue}`},
                                {name: `hotfix/${selectedIssue}`, value: `hotfix/${selectedIssue}`},
                                {name: 'Editar manualmente', value: 'manual'},
                            ],
                        },
                    ]);

                    if (branchChoice === 'manual') {
                        const {manualBranch} = await inquirer.prompt([
                            {
                                type: 'input',
                                name: 'manualBranch',
                                message: 'Informe a branch:',
                                default: selectedIssue, // Preenche com a chave selecionada
                            },
                        ]);
                        branchName = manualBranch;
                    } else {
                        branchName = branchChoice;
                    }
                    const branch_name_normalized = normalizeBranchName(branchName);
                    changeComposeServiceTags(selected_services, branch_name_normalized);
                    await updateComposeServiceTags(selected_services.join(' '));
                    await deployCompose(selected_services.join(' '));
                } else if (branchOption === 'Buscar no Jira - Automatizado') {
                    const jiraEmail = getValue('jira.email', process.env.JIRA_EMAIL);
                    const jiraToken = getValue('jira.token', process.env.JIRA_TOKEN);
                    if (jiraEmail === undefined || jiraToken === undefined) {
                        console.error('Configuração do Jira não encontrada. Use "pacto jira credenciais" para configurar.');
                        return;
                    }

                    const jiraFilters = getValue('jira.filters');
                    if (jiraFilters === undefined || jiraFilters.jql === undefined) {
                        console.error('Nenhuma configuração de busca encontrada. Use "pacto jira config-busca" para configurar.');
                        return;
                    }

                    console.log(`Realizando busca no Jira com o filtro: ${jiraFilters.jql}`);
                    const issues = await searchIssues(jiraFilters.jql);
                    if (issues.length === 0) {
                        console.log('Nenhuma atividade encontrada no Jira.');
                        return;
                    }

                    const {selectedIssue} = await inquirer.prompt([
                        {
                            type: 'autocomplete',
                            name: 'selectedIssue',
                            message: 'Selecione uma atividade:',
                            source: (answersSoFar, input) => {
                                input = input || '';
                                return new Promise((resolve) => {
                                    const filteredIssues = issues.filter(issue =>
                                        issue.fields.summary.toLowerCase().includes(input.toLowerCase()) ||
                                        issue.key.toLowerCase().includes(input.toLowerCase())
                                    );
                                    resolve(filteredIssues.map(issue => ({
                                        name: `${issue.key} -> ${issue.fields.summary}`,
                                        value: issue.key,
                                    })));
                                });
                            },
                        },
                    ]);

                    const weblinks = await getIssueWithWeblinks(selectedIssue);
                    const gitlabMergeRequests = weblinks.filter(link =>
                        link.url.includes('gitlab') &&
                        link.url.includes('merge_requests')
                    );

                    if (gitlabMergeRequests === undefined) {
                        console.log('Nenhum Merge Request do GitLab encontrado em Web links para esta atividade.');
                        return;
                    }

                    const gitlabMergeRequestsBranchs = [];

                    for (const mr of gitlabMergeRequests) {
                        const branchs = await getMergeRequestBranches(mr.url);
                        const branch = branchs.status === 'merged' ? branchs.targetBranch : branchs.sourceBranch;
                        const servicesNames = getServiceNameByGitlabMergeRequestUrl(mr.url);
                        const branch_name_normalized = normalizeBranchName(branch);
                        servicesNames.forEach(serviceName => {
                            if (Array.isArray(serviceName)) {
                                serviceName.forEach(name => {
                                    gitlabMergeRequestsBranchs.push({
                                        sourceBranch: branchs.sourceBranch,
                                        tagName: branch_name_normalized,
                                        serviceName: name,
                                    });
                                });
                            } else {
                                gitlabMergeRequestsBranchs.push({
                                    sourceBranch: branchs.sourceBranch,
                                    tagName: branch_name_normalized,
                                    serviceName,
                                });
                            }
                        });
                    }

                    changeComposeMultipleServiceTags(gitlabMergeRequestsBranchs);
                    const serviceNamesString = gitlabMergeRequestsBranchs.map(mr => {
                        if (Array.isArray(mr.serviceName)) {
                            return mr.serviceName.join(' ');
                        } else {
                            return mr.serviceName;
                        }
                    }).join(' ');

                    await updateComposeServiceTags(serviceNamesString);

                    const selectedServices = gitlabMergeRequestsBranchs.map(mr => mr.serviceName);
                    const selectedServicesString = selectedServices.join(' ');
                    await deployCompose(selectedServicesString);

                } else if (branchOption === 'Versões') {
                    // Nova opção para versões predefinidas
                    const {versionOption} = await inquirer.prompt([
                        {
                            type: 'list',
                            name: 'versionOption',
                            message: 'Selecione a versão:',
                            choices: ['Release 5%', 'Master', 'Estável (Latest)', 'Develop'],
                        },
                    ]);

                    const branchUpdates = getPredefinedVersionBranches(versionOption);
                    const services = getPredefinedVersionServices();

                    changeComposeMultipleServiceTags(branchUpdates);
                    const serviceNamesString = services.join(' ');
                    await updateComposeServiceTags(serviceNamesString);
                    await deployCompose(serviceNamesString);

                } else {

                    const services = getComposeServices();
                    const {selected_services} = await inquirer.prompt([
                        {
                            type: 'checkbox-plus',
                            name: 'selected_services',
                            message: 'Selecione os serviços para alterar a branch:',
                            pageSize: 10,
                            highlight: true,
                            searchable: true,
                            validate: (answer) => {
                                if (answer.length < 1) {
                                    return 'Você deve selecionar pelo menos um serviço.';
                                }
                                return true;
                            },
                            source: (answersSoFar, input) => {
                                input = input || '';
                                return new Promise((resolve) => {
                                    const filteredServices = services.filter((service) =>
                                        service.toLowerCase().includes(input.toLowerCase())
                                    );
                                    resolve(filteredServices.map((service) => ({name: service, value: service})));
                                });
                            },
                        },
                    ]);

                    const {manualBranch} = await inquirer.prompt([
                        {
                            type: 'input',
                            name: 'manualBranch',
                            message: 'Informe o nome da branch:',
                        },
                    ]);
                    branchName = manualBranch;

                    const branch_name_normalized = normalizeBranchName(branchName);
                    changeComposeServiceTags(selected_services, branch_name_normalized);
                    await updateComposeServiceTags(selected_services.join(' '));
                    await deployCompose(selected_services.join(' '));
                }
            } catch (err) {
                logError(`🌿💥 Erro ao alterar as branches dos containers: ${err}`);
            }
        });


    d.command('update')
        .alias('up')
        .description('Atualiza branchs de containers em execução')
        .action(async () => {
            try {
                const services = await getComposeServices();
                const {selected_services} = await inquirer.prompt([
                    {
                        type: 'checkbox-plus',
                        name: 'selected_services',
                        message: 'Selecione os serviços para alterar a branch:',
                        pageSize: 10,
                        highlight: true,
                        searchable: true,
                        validate: (answer) => {
                            if (answer.length < 1) {
                                return 'Você deve selecionar pelo menos um serviço.';
                            }
                            return true;
                        },
                        source: (answersSoFar, input) => {
                            input = input || '';
                            return new Promise((resolve) => {
                                const filteredServices = services.filter(service => service.toLowerCase().includes(input.toLowerCase()));
                                resolve(filteredServices.map(service => ({name: service, value: service})));
                            });
                        }
                    }
                ]);

                await updateComposeServiceTags(selected_services.join(' '));
                await deployCompose(selected_services.join(' '));

            } catch (err) {
                logError(`🌿💥 Erro ao alterar as branches dos containers: ${err}`);
            }
        });

    d.command('reset-branchs')
        .alias('r')
        .description('Reseta as branchs das apliações deployadas para das branchs padrão (master na maioria dos serviços)')
        .action(() => {
            resetBranchs();
        });





    d.command('log')
        .alias('l')
        .description('Log do docker-compose')
        .action(async () => {
            try {
                const containersList = await getRunningContainers();

                if (containersList.length === 0) {
                    logInfo('😴 Nenhum container em execução no momento. Todos dormindo!');
                    return;
                }

                logContainer('🐳 Containers disponíveis:', containersList.join(', '));

                // Perguntar como o usuário quer selecionar os containers
                const {selectionMode} = await inquirer.prompt([
                    {
                        type: 'list',
                        name: 'selectionMode',
                        message: '🎯 Como você quer selecionar os containers?',
                        choices: [
                            {name: '📦 Por stacks predefinidas', value: 'stack'},
                            {name: '🔍 Seleção individual', value: 'individual'}
                        ]
                    }
                ]);

                let selectedContainers = [];

                if (selectionMode === 'stack') {
                    // Obter stacks disponíveis
                    const availableStacks = getComposeServiceGoupNames();

                    // Filtrar apenas stacks que têm pelo menos um container em execução
                    const stacksWithRunningContainers = availableStacks.filter(stackName => {
                        const stackServices = getComposeServicesGroupByName(stackName);
                        return stackServices && stackServices.some(service =>
                            containersList.some(container => container.includes(service))
                        );
                    });

                    if (stacksWithRunningContainers.length === 0) {
                        logInfo('🤷‍♂️ Nenhuma stack tem containers em execução. Que tal fazer um deploy primeiro?');
                        return;
                    }

                    const {selectedStack} = await inquirer.prompt([
                        {
                            type: 'list',
                            name: 'selectedStack',
                            message: '🏗️ Selecione a stack para ver os logs:',
                            choices: stacksWithRunningContainers.map(stack => ({
                                name: `📚 ${stack}`,
                                value: stack
                            }))
                        }
                    ]);

                    // Obter serviços da stack selecionada que estão em execução
                    const stackServices = getComposeServicesGroupByName(selectedStack);
                    selectedContainers = stackServices.filter(service =>
                        containersList.some(container => container.includes(service))
                    );

                    logContainer(`🎬 Stack ${selectedStack} selecionada! Containers:`, selectedContainers.join(', '));

                } else {
                    // Seleção individual (comportamento original)
                    const {containers} = await inquirer.prompt([
                        {
                            type: 'checkbox-plus',
                            name: 'containers',
                            message: 'Selecione os containers para ver os logs:',
                            pageSize: 10,
                            highlight: true,
                            searchable: true,
                            validate: (answer) => {
                                if (answer.length < 1) {
                                    return 'Você deve selecionar pelo menos um container.';
                                }
                                return true;
                            },
                            source: (answersSoFar, input) => {
                                input = input || '';
                                return new Promise((resolve) => {
                                    const filteredContainers = containersList.filter((container) =>
                                        container.toLowerCase().includes(input.toLowerCase())
                                    );
                                    resolve(filteredContainers.map((container) => ({name: container, value: container})));
                                });
                            },
                        },
                    ]);
                    selectedContainers = containers;
                }

                if (selectedContainers.length === 0) {
                    logInfo('🤷‍♂️ Nenhum container selecionado. Que tal escolher alguns?');
                    return;
                }

                logContainer('🎯 Containers selecionados para o show:', selectedContainers.join(', '));
                const composeCommand = getDockerComposeCommand();
                const logsCommand = `${composeCommand} -p dev-tools logs -f --tail=100 ${selectedContainers.join(' ')}`;
                logInfo('🔧 Comando mágico que será executado:', logsCommand);

                // Executa o comando para ver os logs
                execSync(logsCommand, {stdio: 'inherit'});


            } catch (error) {
                if (error.signal === 'SIGINT') {
                    logInfo('👋 Execução interrompida pelo usuário. Até mais!');
                } else {
                    logError('📜💥 Erro ao obter os logs:', error);
                }
            }
        });


}


module.exports = {addDeployCommands};
