const fs = require('fs');
const { exec } = require('child_process');
const path = require('path');

function geComposeUrlsFromEnv() {
    const composeFilePath = path.resolve(__dirname, 'docker-compose.yml');
    const composeFile = fs.readFileSync(composeFilePath, 'utf8');
    const urlLines = composeFile.split('\n').filter(line => line.includes('URL'));
    const urls = urlLines.map(line => {
        const [key, value] = line.split('=');
        return { name: key.trim(), value: value.trim() };
    });
    return urls;
}

function getUrlsFromDockerCompose() {
    const composeFilePath = path.resolve(__dirname, 'docker-compose.yml');
    const composeFile = fs.readFileSync(composeFilePath, 'utf8');

    // Filtrar apenas linhas que contêm URLs de serviços (começam com URL_)
    const urlLines = composeFile.split('\n').filter(line => {
        const trimmedLine = line.trim();
        // Deve conter URL_ no início da chave e ter formato chave: valor
        return trimmedLine.includes('URL_') && trimmedLine.includes(':') &&
               !trimmedLine.includes('USAR_URL_') && // Excluir configurações booleanas
               !trimmedLine.includes('TOKEN_') &&   // Excluir tokens
               !trimmedLine.includes('DYNAMODB');   // Excluir URLs de DynamoDB
    });

    const urlsMap = new Map(); // Usar Map para evitar duplicatas

    urlLines.forEach(line => {
        const colonIndex = line.indexOf(':');
        if (colonIndex > 0) {
            const key = line.substring(0, colonIndex).trim();
            const value = line.substring(colonIndex + 1).trim();

            // Extrair apenas o nome da variável (remover espaços e indentação)
            const cleanKey = key.replace(/^\s+/, ''); // Remove espaços do início

            // Apenas adicionar se começar com URL_ e tiver um valor válido
            if (cleanKey.startsWith('URL_') && value && !value.includes('${')) {
                urlsMap.set(cleanKey, { name: cleanKey, value: value });
            }
        }
    });

    return Array.from(urlsMap.values());
}

function getUrlsFromPostgresContainer() {
    return new Promise((resolve, reject) => {
        exec('docker ps --filter "ancestor=registry.gitlab.com/plataformazw/docker-pacto/postgres:9.4" --format "{{.ID}}"', (err, stdout, stderr) => {
            if (err) {
                console.error(`Erro ao encontrar o container do PostgreSQL: ${err.message}`);
                reject(err);
                return;
            }
            const containerId = stdout.trim();
            if (!containerId) {
                reject(new Error('Nenhum container do PostgreSQL encontrado.'));
                return;
            }

            exec(`docker exec ${containerId} printenv`, (err, stdout, stderr) => {
                if (err) {
                    console.error(`Erro ao inspecionar o container do PostgreSQL: ${err.message}`);
                    reject(err);
                    return;
                }
                const envVars = stdout.split('\n');
                const urls = envVars
                    .filter(env => {
                        return env.includes('URL') && 
                        !env.includes('TOKEN_CURL_DOWNLOAD') &&
                        !env.includes('URL_DYNAMODB') &&
                        !env.includes('USAR_URL_RECURSO_EMPRESA');
                    })
                    .map(env => {
                        const [key, value] = env.split('=');
                        return { name: key, value: value };
                    });
                if (urls.length > 0) {
                    resolve(urls);
                } else {
                    reject(new Error('Nenhuma variável de ambiente contendo URL encontrada.'));
                }
            });
        });
    });
}

module.exports = { geComposeUrlsFromEnv, getUrlsFromPostgresContainer, getUrlsFromDockerCompose };