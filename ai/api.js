const { setValue, getValue } = require('../util/storage');
const { findIssuesByProject } = require('./review');
const axios = require('axios');
const inquirer = require('inquirer');

const DEFAULT_API_URLS = {
  openai: 'https://api.openai.com/v1/chat/completions',
  gemini: 'https://generativelanguage.googleapis.com/v1beta/models',
  anthropic: 'https://api.anthropic.com/v1/messages',
  deepseek: 'https://api.deepseek.com/v1/chat/completions',
  grok: 'https://api.grok.ai/v1/chat/completions',
};

// Função para obter o token de qualquer modelo
async function getModelToken(provider) {
  // Verificar variável de ambiente primeiro
  const envVarName = `${provider.toUpperCase()}_API_KEY`;
  const logProvider = getValue(envVarName + '_provider', false);
  if (process.env[envVarName]) {
    if (!logProvider) {
      console.log(`Usando token do ambiente: ${envVarName}`);
    }
    setValue(`${provider}_token`, process.env[envVarName]);

    return process.env[envVarName];
  }

  // Verificar se já temos o token armazenado
  let token = getValue(`${provider}_token`);
  if (!token) {
    // Solicitar o token ao usuário
    const modelDisplayNames = {
      openai: 'OpenAI',
      gemini: 'Google Gemini',
      anthropic: 'Anthropic Claude',
      deepseek: 'Deepseek',
      grok: 'xAI Grok',
    };

    const displayName = modelDisplayNames[provider] || provider;

    const answers = await inquirer.prompt([
      {
        type: 'password',
        name: 'token',
        message: `Por favor, insira seu token da ${displayName}:`,
        validate: (input) =>
          input.trim() !== '' ? true : 'O token não pode estar vazio',
      },
    ]);

    token = answers.token;
    setValue(`${provider}_token`, token);
  }

  return token;
}

// Função específica para OpenAI (mantida para compatibilidade)
async function getOpenApiToken() {
  return getModelToken('openai');
}

const getSystemPrompt = (
  diff_refs,
  observations,
  isInlineMode = false,
  selectedProjectId
) => {
  const diff_refs_text = JSON.stringify(diff_refs);

  let issuesText = '';
  if (selectedProjectId) {
    const issues = findIssuesByProject(selectedProjectId);
    issuesText = issues.map((issue) => issue.issues).join('\n');
  }

  if (isInlineMode) {
    let prompt =
      'Você é um especialista em revisão de código. ' +
      'Você deve revisar o código diff e fornecer feedback sobre possíveis melhorias específicas para cada linha problemática.' +
      'Você deve retornar SOMENTE um array JSON válido, onde cada objeto representa um comentário inline específico. ' +
      'Escreva seu review sempre em português do brasil. ' +
      'Cada objeto deve ter esta estrutura exata: ' +
      `[{` +
      `"comment": "Texto do comentário explicando a melhoria específica para esta linha",` +
      `"position": {` +
      `"new_path": "caminho/do/arquivo.java",` +
      `"new_line": 123` +
      `}` +
      `}]` +
      'IMPORTANTE: ' +
      '- Analise apenas as linhas que foram ADICIONADAS (+) no diff.' +
      '- Use o número da linha EXATO do diff (new_line).' +
      '- Use o caminho EXATO do arquivo (new_path).' +
      '- Se não houver problemas específicos, retorne um array vazio [].' +
      '- Foque em problemas reais, não comentários genéricos.' +
      'Mostre sempre um exemplo de melhoria para para cada problema/melhoria encontrada.' +
      'Destaque com ênfase os erros indentificados.' +
      '- Mostre sempre um exemplo de solução para o problema encontrado.' +
      'Você vai separar o feedback em 5 categorias: ' +
      '1. Analise se tem código duplicado' +
      '2. Problemas de performance' +
      '3. Problemas de segurança' +
      '4. Problemas de qualidade de código' +
      '5. Problemas crônicos do projeto que são: ' +
      issuesText;

    if (observations) {
      prompt += ' Observações adicionais: ' + observations;
    }

    return prompt;
  } else {
    // Prompt original para comentário geral
    let prompt =
      'Você é um especialista em revisão de código. ' +
      'Você deve revisar o código diff e fornecer feedback sobre possíveis melhorias explicando e mostrando qual é o arquivo e a linha.' +
      'Você deve escrever o feedback em português.' +
      'Mostre sempre um exemplo de melhoria para para cada problema/melhoria encontrada.' +
      'Destaque com ênfase os erros indentificados.' +
      'Você deve criar um checklist de melhorias por categoria: ' +
      'Se a categoria tem alguma melhoria ou bug, você deve colocar um icone de alerta na categoria.' +
      'Se a categoria não tem nenhuma melhoria ou bug, você deve colocar um icone de ok na categoria.' +
      'Você vai separar o feedback em 5 categorias: ' +
      '1. Analise se tem código duplicado' +
      '2. Problemas de performance' +
      '3. Problemas de segurança' +
      '4. Problemas de qualidade de código' +
      '5. Problemas crônicos do projeto que são: ' +
      issuesText;

    if (observations) {
      prompt += 'Observações: ' + observations;
    }

    return prompt;
  }
};

const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

async function makeRequestWithRetry(
  file_diff,
  systemPrompt,
  openaiToken,
  maxRetries = 5
) {
  let retries = 0;
  let backoffTime = 2000;

  // Obter o modelo e provedor configurados
  const provider = getValue('ai_model') || 'openai';
  let token, model, apiUrl;

  // Obter configurações específicas do modelo
  switch (provider) {
    case 'openai':
      token = await getModelToken('openai');
      model = getValue('openai_model') || 'gpt-4';
      apiUrl = getValue('openai_api_url') || DEFAULT_API_URLS.openai;
      break;
    case 'gemini':
      token = await getModelToken('gemini');
      model = getValue('gemini_model') || 'gemini-pro';
      apiUrl = getValue('gemini_api_url') || DEFAULT_API_URLS.gemini;
      break;
    case 'anthropic':
      token = await getModelToken('anthropic');
      model = getValue('anthropic_model') || 'claude-3-opus-20240229';
      apiUrl = getValue('anthropic_api_url') || DEFAULT_API_URLS.anthropic;
      break;
    case 'deepseek':
      token = await getModelToken('deepseek');
      model = getValue('deepseek_model') || 'deepseek-coder';
      apiUrl = getValue('deepseek_api_url') || DEFAULT_API_URLS.deepseek;
      break;
    case 'grok':
      token = await getModelToken('grok');
      model = getValue('grok_model') || 'grok-1';
      apiUrl = getValue('grok_api_url') || DEFAULT_API_URLS.grok;
      break;
    default:
      // Fallback para OpenAI
      token = openaiToken || (await getModelToken('openai'));
      model = 'gpt-4';
      apiUrl = DEFAULT_API_URLS.openai;
      provider = 'openai';
  }

  while (retries < maxRetries) {
    try {
      let response;

      switch (provider) {
        case 'openai':
          response = await axios.post(
            apiUrl,
            {
              model: model,
              messages: [
                {
                  role: 'system',
                  content: systemPrompt,
                },
                {
                  role: 'user',
                  content: `Por favor revise em português este code diff:\n${JSON.stringify(file_diff)}`,
                },
              ],
            },
            {
              headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
              },
            }
          );
          return response.data.choices[0].message.content;

        case 'gemini':
          // Para o Gemini, a URL base é diferente da URL completa
          const geminiFullUrl = `${apiUrl}/${model}:generateContent`;
          response = await axios.post(
            geminiFullUrl,
            {
              contents: [
                {
                  role: 'user',
                  parts: [
                    { text: systemPrompt },
                    {
                      text: `Por favor revise em português este code diff:\n${JSON.stringify(file_diff)}`,
                    },
                  ],
                },
              ],
            },
            {
              headers: {
                'Content-Type': 'application/json',
                'x-goog-api-key': token,
              },
            }
          );
          return response.data.candidates[0].content.parts[0].text;

        case 'anthropic':
          response = await axios.post(
            apiUrl,
            {
              model: model,
              max_tokens: 4000,
              messages: [
                {
                  role: 'user',
                  content:
                    systemPrompt +
                    `\n\nPor favor revise em português este code diff:\n${JSON.stringify(file_diff)}`,
                },
              ],
            },
            {
              headers: {
                'Content-Type': 'application/json',
                'x-api-key': token,
                'anthropic-version': '2023-06-01',
              },
            }
          );
          return response.data.content[0].text;

        case 'deepseek':
          response = await axios.post(
            apiUrl,
            {
              model: model,
              messages: [
                {
                  role: 'system',
                  content: systemPrompt,
                },
                {
                  role: 'user',
                  content: `Por favor revise em português este code diff:\n${JSON.stringify(file_diff)}`,
                },
              ],
            },
            {
              headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
              },
            }
          );
          return response.data.choices[0].message.content;

        case 'grok':
          response = await axios.post(
            apiUrl,
            {
              model: model,
              messages: [
                {
                  role: 'system',
                  content: systemPrompt,
                },
                {
                  role: 'user',
                  content: `Por favor revise em português este code diff:\n${JSON.stringify(file_diff)}`,
                },
              ],
            },
            {
              headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
              },
            }
          );
          return response.data.choices[0].message.content;

        default:
          throw new Error(`Provedor de IA não suportado: ${provider}`);
      }
    } catch (error) {
      if (error.response && error.response.status === 429) {
        retries++;
        console.log(
          `Rate limit atingido. Tentativa ${retries}/${maxRetries}. Aguardando ${backoffTime / 1000} segundos...`
        );

        await sleep(backoffTime);
        backoffTime *= 2;

        if (retries < maxRetries) continue;
      }

      throw error;
    }
  }

  throw new Error('Número máximo de tentativas excedido');
}

async function getAICodeReview(
  diff,
  observations,
  mrData,
  isInlineMode = false,
  selectedProjectId
) {
  const openaiToken = await getOpenApiToken(); // Mantido para compatibilidade
  const { diff_refs } = mrData;
  const systemPrompt = getSystemPrompt(
    diff_refs,
    observations,
    isInlineMode,
    selectedProjectId
  );
  let reviews = [];

  // Obter informações do modelo configurado para exibição
  const provider = getValue('ai_model') || 'openai';
  const model =
    getValue(`${provider}_model`) ||
    (provider === 'openai'
      ? 'gpt-4'
      : provider === 'gemini'
        ? 'gemini-pro'
        : provider === 'anthropic'
          ? 'claude-3-opus-20240229'
          : provider === 'deepseek'
            ? 'deepseek-coder'
            : 'grok-1');

  const modelDisplayNames = {
    openai: 'OpenAI',
    gemini: 'Google Gemini',
    anthropic: 'Anthropic Claude',
    deepseek: 'Deepseek',
    grok: 'xAI Grok',
  };

  console.log(
    `Usando modelo de IA: ${modelDisplayNames[provider] || provider} (${model})`
  );

  // Processar cada arquivo individualmente com um intervalo entre as requisições
  for (const file_diff of diff) {
    const fileName = file_diff.new_path || file_diff.old_path;
    console.log(`Processando revisão para arquivo: ${fileName}`);

    try {
      // Usa a função com retry
      const reviewContent = await makeRequestWithRetry(
        file_diff,
        systemPrompt,
        openaiToken
      );

      reviews.push({
        file: fileName,
        review: reviewContent,
      });

      // Espera um pouco entre as requisições para evitar rate limiting
      await sleep(1000);
    } catch (error) {
      console.error(`Erro ao processar arquivo ${fileName}:`, error.message);
      reviews.push({
        file: fileName,
        error: error.message,
      });

      // Espera um pouco mais após um erro
      await sleep(2000);
    }
  }

  // Formatar o resultado final
  if (isInlineMode) {
    // Para modo inline, retornar os comentários estruturados
    const allComments = [];
    for (const item of reviews) {
      if (item.review && !item.error) {
        try {
          const comments = JSON.parse(item.review);
          if (Array.isArray(comments)) {
            allComments.push(...comments);
          }
        } catch (e) {
          console.warn(
            `Erro ao processar comentários do arquivo ${item.file}:`,
            e.message
          );
        }
      }
    }
    return JSON.stringify(allComments);
  } else {
    // Formato original para comentário geral
    return reviews
      .map(
        (item) =>
          `## Arquivo: ${item.file}\n\n${item.review || 'Erro: ' + item.error}\n\n---\n\n`
      )
      .join('');
  }
}

module.exports = {
  getAICodeReview,
  getModelToken,
  getOpenApiToken,
};
