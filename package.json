{"name": "@plataformazw/dev-tools", "version": "1.0.8", "publishConfig": {"@plataformazw:registry": "https://gitlab.com/api/v4/projects/56628072/packages/npm/"}, "engines": {"node": ">=12"}, "dependencies": {"@vscode/sudo-prompt": "^9.3.1", "axios": "^1.7.2", "chalk": "^4.1.2", "commander": "^5.1.0", "dotenv": "^16.4.5", "figlet": "^1.8.0", "inquirer": "^6.0.0", "inquirer-autocomplete-prompt": "^1.0.2", "inquirer-checkbox-plus-prompt": "^1.4.2", "is-admin": "^4.0.0", "js-yaml": "^4.1.0", "node-emoji": "^1.10.0", "open": "^7.0.0", "ora": "4.1.1", "ping": "^0.4.4", "sudo-prompt": "^9.2.1", "unzipper": "^0.11.6", "url": "^0.11.3", "wait-on": "^7.2.0"}, "scripts": {"pacto": "node ./index.js"}, "bin": {"pacto": "./index.js", "p": "./index.js"}}